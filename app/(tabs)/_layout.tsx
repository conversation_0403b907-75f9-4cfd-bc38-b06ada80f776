import { MaterialIcons } from '@expo/vector-icons';
import { Tabs } from 'expo-router';
import React from 'react';
import { Platform, StyleSheet, TouchableOpacity, View } from 'react-native';

import { HapticTab } from '@/components/HapticTab';
import TabBarBackground from '@/components/ui/TabBarBackground';

// 自定義中央浮動按鈕組件
function FloatingActionButton() {
  const handlePress = () => {
    console.log('Floating action button pressed');
    // TODO: 實現添加功能
  };

  return (
    <TouchableOpacity
      style={styles.floatingButton}
      onPress={handlePress}
      activeOpacity={0.8}
    >
      <MaterialIcons name="add" size={32} color="#FFFFFF" />
    </TouchableOpacity>
  );
}

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#4F46E5', // indigo-600
        tabBarInactiveTintColor: '#64748B', // slate-500
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: {
          height: 96, // 增加高度以容納浮動按鈕
          paddingBottom: Platform.OS === 'ios' ? 34 : 16, // 考慮安全區域
          paddingTop: 16,
          backgroundColor: '#FFFFFF',
          borderTopWidth: 1,
          borderTopColor: '#E2E8F0', // slate-200
          elevation: 8,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: -4,
          },
          shadowOpacity: 0.1,
          shadowRadius: 6,
          ...Platform.select({
            ios: {
              position: 'absolute',
            },
            default: {},
          }),
        },
        tabBarLabelStyle: {
          fontSize: 14,
          fontWeight: '500',
          marginTop: 6,
        },
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color, focused }) => (
            <MaterialIcons
              name="home"
              size={32}
              color={focused ? '#4F46E5' : '#64748B'}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="add"
        options={{
          title: '',
          tabBarIcon: () => <FloatingActionButton />,
          tabBarButton: (props) => (
            <View style={styles.floatingButtonContainer}>
              <TouchableOpacity
                onPress={props.onPress}
                style={styles.floatingButtonWrapper}
                activeOpacity={0.8}
              >
                <View style={styles.floatingButton}>
                  <MaterialIcons name="add" size={32} color="#FFFFFF" />
                </View>
              </TouchableOpacity>
            </View>
          ),
        }}
        listeners={{
          tabPress: (e) => {
            e.preventDefault();
            console.log('Add button pressed');
            // TODO: 實現添加功能
          },
        }}
      />
      <Tabs.Screen
        name="explore"
        options={{
          title: 'Notifications',
          tabBarIcon: ({ color, focused }) => (
            <MaterialIcons
              name="notifications"
              size={32}
              color={focused ? '#4F46E5' : '#64748B'}
            />
          ),
        }}
      />
    </Tabs>
  );
}

// 樣式定義
const styles = StyleSheet.create({
  floatingButtonContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  floatingButtonWrapper: {
    marginTop: -40, // 向上偏移以創建浮動效果
  },
  floatingButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#4F46E5', // indigo-600
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
});
