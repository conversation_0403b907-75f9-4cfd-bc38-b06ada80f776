import { MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import {
    Appbar,
    Avatar,
    Button,
    Card,
    Text
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

// 主頁面組件 - 實現 CareComms 應用的主界面
export default function HomeScreen() {

  // 處理卡片點擊事件
  const handleCardPress = (cardType: string) => {
    console.log(`${cardType} card pressed`);
    // TODO: 實現導航邏輯
  };

  // 處理快速操作按鈕點擊
  const handleQuickAction = (actionType: string) => {
    console.log(`${actionType} action pressed`);
    // TODO: 實現相應的功能
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* 頂部標題欄 */}
      <Appbar.Header style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.logoContainer}>
            <Avatar.Image
              size={40}
              source={{ uri: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCFiSh23snutDa79cLr4-HtIJZ6A4tk8Wbqhy7HOW1-uH8cgVMVxnK0VCXWFNtMYLIvT9eYoiGhg97iHB5uFNyUqqAjgEaNGjpxIxUw72XXJ_sxHuiCgdTmxqss-M6poAh1cHmu4Ynndc2REpshiKY2UvnfNpo6fecP4ylaRIZfd-6v3sGqgo0wvzh4VLFbJVn9D1qHBoGJoz2FSzEaYzYNJ2CwGykhxOzfIVyYVzz0xnedvU3ZyCePOdzobyJtoz8mjPsEsqwd4Es' }}
              style={styles.logo}
            />
            <Text style={styles.appTitle}>CareComms</Text>
          </View>
        </View>
      </Appbar.Header>

      {/* 主要內容區域 */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* 功能卡片網格 */}
        <View style={styles.cardGrid}>
          {/* Profile 卡片 */}
          <TouchableOpacity
            style={styles.cardWrapper}
            onPress={() => handleCardPress('Profile')}
            activeOpacity={0.7}
          >
            <Card style={[styles.card, styles.profileCard]}>
              <Card.Content style={styles.cardContent}>
                <View style={[styles.iconContainer, styles.profileIconContainer]}>
                  <MaterialIcons name="person" size={28} color="#4F46E5" />
                </View>
                <Text style={styles.cardText}>Profile</Text>
              </Card.Content>
            </Card>
          </TouchableOpacity>

          {/* Edit Group 卡片 */}
          <TouchableOpacity
            style={styles.cardWrapper}
            onPress={() => handleCardPress('EditGroup')}
            activeOpacity={0.7}
          >
            <Card style={[styles.card, styles.editGroupCard]}>
              <Card.Content style={styles.cardContent}>
                <View style={[styles.iconContainer, styles.editGroupIconContainer]}>
                  <MaterialIcons name="edit" size={28} color="#0284C7" />
                </View>
                <Text style={styles.cardText}>Edit Group</Text>
              </Card.Content>
            </Card>
          </TouchableOpacity>
        </View>

        {/* Quick Actions 區域 */}
        <Card style={styles.quickActionsCard}>
          <Card.Content>
            <Text style={styles.quickActionsTitle}>Quick Actions</Text>

            {/* Search User 按鈕 */}
            <Button
              mode="contained-tonal"
              onPress={() => handleQuickAction('SearchUser')}
              style={[styles.actionButton, styles.searchUserButton]}
              contentStyle={styles.actionButtonContent}
              labelStyle={styles.actionButtonLabel}
              icon={({ size, color }) => (
                <MaterialIcons name="search" size={size} color="#7C3AED" />
              )}
            >
              Search User
            </Button>

            {/* View Notifications 按鈕 */}
            <Button
              mode="contained-tonal"
              onPress={() => handleQuickAction('ViewNotifications')}
              style={[styles.actionButton, styles.viewNotificationsButton]}
              contentStyle={styles.actionButtonContent}
              labelStyle={styles.actionButtonLabel}
              icon={({ size, color }) => (
                <MaterialIcons name="visibility" size={size} color="#2563EB" />
              )}
            >
              View My Initiated Notifications
            </Button>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

// 樣式定義 - 遵循 Material Design 原則
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    backgroundColor: '#4F46E5', // indigo-600
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  headerContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logo: {
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  appTitle: {
    marginLeft: 12,
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 24,
  },
  cardGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    gap: 16,
  },
  cardWrapper: {
    flex: 1,
  },
  card: {
    backgroundColor: '#F8FAFC', // slate-50
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    borderRadius: 12,
    aspectRatio: 1, // 正方形卡片
  },
  profileCard: {
    // Profile 卡片特定樣式
  },
  editGroupCard: {
    // Edit Group 卡片特定樣式
  },
  cardContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  profileIconContainer: {
    backgroundColor: '#E0E7FF', // indigo-100
  },
  editGroupIconContainer: {
    backgroundColor: '#E0F2FE', // sky-100
  },
  cardText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#475569', // slate-700
    textAlign: 'center',
  },
  quickActionsCard: {
    backgroundColor: '#F8FAFC', // slate-50
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    borderRadius: 12,
    padding: 24,
  },
  quickActionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#475569', // slate-700
    marginBottom: 16,
  },
  actionButton: {
    marginBottom: 16,
    borderRadius: 8,
  },
  searchUserButton: {
    backgroundColor: '#FAF5FF', // purple-50
  },
  viewNotificationsButton: {
    backgroundColor: '#EFF6FF', // blue-50
  },
  actionButtonContent: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  actionButtonLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
});
