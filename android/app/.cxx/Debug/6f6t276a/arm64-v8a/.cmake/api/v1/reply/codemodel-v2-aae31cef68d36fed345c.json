{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9], "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-cf22d66d50f9d19200cf.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [5]}, {"build": "VectorIconsMaterialDesignIcons_autolinked_build", "jsonFile": "directory-VectorIconsMaterialDesignIcons_autolinked_build-Debug-f1a76cac865b0b404867.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-vector-icons/material-design-icons/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-5b4f24301c6034d82bcc.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [6]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-Debug-bc41cffe9a83472625e4.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [7]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-a97394d0dc9ed1aa289f.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [9]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-000b35c8df564794f73e.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [8]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-99e59fd4ba94b92eaa39.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "RNCWebViewSpec_autolinked_build", "jsonFile": "directory-RNCWebViewSpec_autolinked_build-Debug-8ea68d0f91a3169d52b5.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-webview/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "RNEdgeToEdge_autolinked_build", "jsonFile": "directory-RNEdgeToEdge_autolinked_build-Debug-a964e5789e519d2b476f.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni", "targetIndexes": [2]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-6bca51ef51d06bdb592b.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 8, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c", "jsonFile": "target-react_codegen_RNCWebViewSpec-Debug-4e9374f6f02d22882888.json", "name": "react_codegen_RNCWebViewSpec", "projectIndex": 0}, {"directoryIndex": 9, "id": "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e", "jsonFile": "target-react_codegen_RNEdgeToEdge-Debug-2fba600229ed7e0e69f9.json", "name": "react_codegen_RNEdgeToEdge", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-a1e9ae68f7c02609fb67.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_VectorIconsMaterialDesignIcons::@ec6cd781043477cf3896", "jsonFile": "target-react_codegen_VectorIconsMaterialDesignIcons-Debug-62fc651ebc47cbccbdf1.json", "name": "react_codegen_VectorIconsMaterialDesignIcons", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-5f8d810a936018d7d0ef.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-daabdbe438dec433f6ad.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-Debug-a3e6fb48f4eb030e998a.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-9ab9578abbae8b8cc0c6.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-9be332b95efae8c09e90.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/.cxx/Debug/6f6t276a/arm64-v8a", "source": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}