{"artifacts": [{"path": "VectorIconsMaterialDesignIcons_autolinked_build/CMakeFiles/react_codegen_VectorIconsMaterialDesignIcons.dir/./VectorIconsMaterialDesignIcons-generated.cpp.o"}, {"path": "VectorIconsMaterialDesignIcons_autolinked_build/CMakeFiles/react_codegen_VectorIconsMaterialDesignIcons.dir/./react/renderer/components/VectorIconsMaterialDesignIcons/ComponentDescriptors.cpp.o"}, {"path": "VectorIconsMaterialDesignIcons_autolinked_build/CMakeFiles/react_codegen_VectorIconsMaterialDesignIcons.dir/./react/renderer/components/VectorIconsMaterialDesignIcons/EventEmitters.cpp.o"}, {"path": "VectorIconsMaterialDesignIcons_autolinked_build/CMakeFiles/react_codegen_VectorIconsMaterialDesignIcons.dir/./react/renderer/components/VectorIconsMaterialDesignIcons/Props.cpp.o"}, {"path": "VectorIconsMaterialDesignIcons_autolinked_build/CMakeFiles/react_codegen_VectorIconsMaterialDesignIcons.dir/./react/renderer/components/VectorIconsMaterialDesignIcons/ShadowNodes.cpp.o"}, {"path": "VectorIconsMaterialDesignIcons_autolinked_build/CMakeFiles/react_codegen_VectorIconsMaterialDesignIcons.dir/./react/renderer/components/VectorIconsMaterialDesignIcons/States.cpp.o"}, {"path": "VectorIconsMaterialDesignIcons_autolinked_build/CMakeFiles/react_codegen_VectorIconsMaterialDesignIcons.dir/./react/renderer/components/VectorIconsMaterialDesignIcons/VectorIconsMaterialDesignIconsJSI-generated.cpp.o"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_compile_options", "target_include_directories", "target_link_libraries"], "files": ["/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-vector-icons/material-design-icons/android/build/generated/source/codegen/jni/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 11, "parent": 0}, {"command": 1, "file": 0, "line": 28, "parent": 0}, {"command": 2, "file": 0, "line": 17, "parent": 0}, {"command": 3, "file": 0, "line": 19, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 2, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 2, "fragment": "-fexceptions"}, {"backtrace": 2, "fragment": "-frtti"}, {"backtrace": 2, "fragment": "-std=c++20"}, {"backtrace": 2, "fragment": "-Wall"}, {"backtrace": 0, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 0, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 0, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 0, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "includes": [{"backtrace": 3, "path": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-vector-icons/material-design-icons/android/build/generated/source/codegen/jni/."}, {"backtrace": 3, "path": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-vector-icons/material-design-icons/android/build/generated/source/codegen/jni/react/renderer/components/VectorIconsMaterialDesignIcons"}, {"backtrace": 4, "isSystem": true, "path": "/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 4, "isSystem": true, "path": "/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include"}, {"backtrace": 4, "isSystem": true, "path": "/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6], "sysroot": {"path": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot"}}], "id": "react_codegen_VectorIconsMaterialDesignIcons::@ec6cd781043477cf3896", "name": "react_codegen_VectorIconsMaterialDesignIcons", "paths": {"build": "VectorIconsMaterialDesignIcons_autolinked_build", "source": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-vector-icons/material-design-icons/android/build/generated/source/codegen/jni"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-vector-icons/material-design-icons/android/build/generated/source/codegen/jni/VectorIconsMaterialDesignIcons-generated.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-vector-icons/material-design-icons/android/build/generated/source/codegen/jni/react/renderer/components/VectorIconsMaterialDesignIcons/ComponentDescriptors.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-vector-icons/material-design-icons/android/build/generated/source/codegen/jni/react/renderer/components/VectorIconsMaterialDesignIcons/EventEmitters.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-vector-icons/material-design-icons/android/build/generated/source/codegen/jni/react/renderer/components/VectorIconsMaterialDesignIcons/Props.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-vector-icons/material-design-icons/android/build/generated/source/codegen/jni/react/renderer/components/VectorIconsMaterialDesignIcons/ShadowNodes.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-vector-icons/material-design-icons/android/build/generated/source/codegen/jni/react/renderer/components/VectorIconsMaterialDesignIcons/States.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-vector-icons/material-design-icons/android/build/generated/source/codegen/jni/react/renderer/components/VectorIconsMaterialDesignIcons/VectorIconsMaterialDesignIconsJSI-generated.cpp", "sourceGroupIndex": 0}], "type": "OBJECT_LIBRARY"}