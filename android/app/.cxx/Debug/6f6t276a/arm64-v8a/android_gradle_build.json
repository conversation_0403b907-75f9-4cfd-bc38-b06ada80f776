{"buildFiles": ["/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-vector-icons/material-design-icons/android/build/generated/source/codegen/jni/CMakeLists.txt", "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt", "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt", "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt", "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/.cxx/Debug/6f6t276a/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/.cxx/Debug/6f6t276a/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "appmodules", "output": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/intermediates/cxx/Debug/6f6t276a/obj/arm64-v8a/libappmodules.so", "runtimeFiles": ["/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/intermediates/cxx/Debug/6f6t276a/obj/arm64-v8a/libreact_codegen_safeareacontext.so", "/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/intermediates/cxx/Debug/6f6t276a/obj/arm64-v8a/libreact_codegen_rnscreens.so", "/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so", "/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so", "/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so"]}, "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNCWebViewSpec"}, "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNEdgeToEdge"}, "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNVectorIconsSpec"}, "react_codegen_VectorIconsMaterialDesignIcons::@ec6cd781043477cf3896": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_VectorIconsMaterialDesignIcons"}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnasyncstorage"}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rngesturehandler_codegen"}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnreanimated"}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnscreens", "output": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/intermediates/cxx/Debug/6f6t276a/obj/arm64-v8a/libreact_codegen_rnscreens.so", "runtimeFiles": ["/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so", "/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so", "/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so"]}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_safeareacontext", "output": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/intermediates/cxx/Debug/6f6t276a/obj/arm64-v8a/libreact_codegen_safeareacontext.so", "runtimeFiles": ["/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so", "/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so", "/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}