1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.anonymous.QMNotiAugment"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:4:3-75
11-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:4:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:2:3-64
12-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:2:20-62
13    <uses-permission
13-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:3:3-77
14        android:name="android.permission.READ_EXTERNAL_STORAGE"
14-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:3:20-75
15        android:maxSdkVersion="32" />
15-->[BareExpo:expo.modules.image:2.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/6b8f7dba8018f7d0840d971708dd0e42/transformed/expo.modules.image-2.1.7/AndroidManifest.xml:17:9-35
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:5:3-63
16-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:5:20-61
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:6:3-78
17-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:6:20-76
18
19    <queries>
19-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:7:3-13:13
20        <intent>
20-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:8:5-12:14
21            <action android:name="android.intent.action.VIEW" />
21-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:9:7-58
21-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:9:15-56
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:10:7-67
23-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:10:17-65
24
25            <data android:scheme="https" />
25-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:11:7-37
25-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:11:13-35
26        </intent>
27
28        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
28-->[:expo-dev-launcher] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-53
28-->[:expo-dev-launcher] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:18-50
29        <intent>
29-->[:expo-file-system] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:9-17:18
30            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
30-->[:expo-file-system] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-79
30-->[:expo-file-system] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:21-76
31        </intent>
32        <intent>
32-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/98b17bb88da34f3d70ae40d3a46726b9/transformed/expo.modules.webbrowser-14.1.6/AndroidManifest.xml:8:9-12:18
33
34            <!-- Required for opening tabs if targeting API 30 -->
35            <action android:name="android.support.customtabs.action.CustomTabsService" />
35-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/98b17bb88da34f3d70ae40d3a46726b9/transformed/expo.modules.webbrowser-14.1.6/AndroidManifest.xml:11:13-90
35-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/98b17bb88da34f3d70ae40d3a46726b9/transformed/expo.modules.webbrowser-14.1.6/AndroidManifest.xml:11:21-87
36        </intent>
37    </queries>
38
39    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
40    <!--
41  Allows Glide to monitor connectivity status and restart failed requests if users go from a
42  a disconnected to a connected network state.
43    -->
44    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
44-->[BareExpo:expo.modules.image:2.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/6b8f7dba8018f7d0840d971708dd0e42/transformed/expo.modules.image-2.1.7/AndroidManifest.xml:12:5-79
44-->[BareExpo:expo.modules.image:2.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/6b8f7dba8018f7d0840d971708dd0e42/transformed/expo.modules.image-2.1.7/AndroidManifest.xml:12:22-76
45    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
45-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-81
45-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-78
46    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
46-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-77
46-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:22-74
47    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
47-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:24:5-68
47-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:24:22-65
48    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
48-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:26:5-82
48-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:26:22-79
49
50    <permission
50-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/480ce62020c830cf93653ad6a17bbf11/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
51        android:name="com.anonymous.QMNotiAugment.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
51-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/480ce62020c830cf93653ad6a17bbf11/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
52        android:protectionLevel="signature" />
52-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/480ce62020c830cf93653ad6a17bbf11/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
53
54    <uses-permission android:name="com.anonymous.QMNotiAugment.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- for android -->
54-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/480ce62020c830cf93653ad6a17bbf11/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
54-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/480ce62020c830cf93653ad6a17bbf11/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
55    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
56    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
57    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
58    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
59    <!-- for Samsung -->
60    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
60-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:5-86
60-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:22-83
61    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
61-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:5-87
61-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:22-84
62    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
62-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:5-81
62-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:22-78
63    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
63-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:5-83
63-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:22-80
64    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
64-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:5-88
64-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:22-85
65    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
65-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:5-92
65-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:22-89
66    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
66-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:5-84
66-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:22-81
67    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
67-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:5-83
67-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:22-80
68    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
68-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:5-91
68-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:22-88
69    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
69-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:5-92
69-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:22-89
70    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
70-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:5-93
70-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:22-90
71    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
71-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:5-73
71-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:22-70
72    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
72-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:5-82
72-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:22-79
73    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
73-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:5-83
73-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:22-80
74    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
74-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:5-88
74-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:22-85
75    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
75-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:5-89
75-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/72a7402d6234c31773e846ec2436a878/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:22-86
76    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
76-->[com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/ab892e1514d9c5c4a909d4742382db02/transformed/installreferrer-2.2/AndroidManifest.xml:9:5-110
76-->[com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/ab892e1514d9c5c4a909d4742382db02/transformed/installreferrer-2.2/AndroidManifest.xml:9:22-107
77
78    <application
78-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:14:3-31:17
79        android:name="com.anonymous.QMNotiAugment.MainApplication"
79-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:14:16-47
80        android:allowBackup="true"
80-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:14:162-188
81        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
81-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/480ce62020c830cf93653ad6a17bbf11/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
82        android:debuggable="true"
83        android:extractNativeLibs="false"
84        android:icon="@mipmap/ic_launcher"
84-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:14:81-115
85        android:label="@string/app_name"
85-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:14:48-80
86        android:roundIcon="@mipmap/ic_launcher_round"
86-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:14:116-161
87        android:supportsRtl="true"
87-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:14:221-247
88        android:theme="@style/AppTheme"
88-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:14:189-220
89        android:usesCleartextTraffic="true" >
89-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/debug/AndroidManifest.xml:6:18-53
90        <meta-data
90-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:15:5-83
91            android:name="expo.modules.updates.ENABLED"
91-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:15:16-59
92            android:value="false" />
92-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:15:60-81
93        <meta-data
93-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:16:5-105
94            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
94-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:16:16-80
95            android:value="ALWAYS" />
95-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:16:81-103
96        <meta-data
96-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:17:5-99
97            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
97-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:17:16-79
98            android:value="0" />
98-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:17:80-97
99
100        <activity
100-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:18:5-30:16
101            android:name="com.anonymous.QMNotiAugment.MainActivity"
101-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:18:15-43
102            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
102-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:18:44-134
103            android:exported="true"
103-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:18:256-279
104            android:launchMode="singleTask"
104-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:18:135-166
105            android:screenOrientation="portrait"
105-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:18:280-316
106            android:theme="@style/Theme.App.SplashScreen"
106-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:18:210-255
107            android:windowSoftInputMode="adjustResize" >
107-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:18:167-209
108            <intent-filter>
108-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:19:7-22:23
109                <action android:name="android.intent.action.MAIN" />
109-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:20:9-60
109-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:20:17-58
110
111                <category android:name="android.intent.category.LAUNCHER" />
111-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:21:9-68
111-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:21:19-66
112            </intent-filter>
113            <intent-filter>
113-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:23:7-29:23
114                <action android:name="android.intent.action.VIEW" />
114-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:9:7-58
114-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:9:15-56
115
116                <category android:name="android.intent.category.DEFAULT" />
116-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:25:9-67
116-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:25:19-65
117                <category android:name="android.intent.category.BROWSABLE" />
117-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:10:7-67
117-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:10:17-65
118
119                <data android:scheme="qmnotiaugment" />
119-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:11:7-37
119-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:11:13-35
120                <data android:scheme="exp+qmnotiaugment" />
120-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:11:7-37
120-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:11:13-35
121            </intent-filter>
122        </activity>
123
124        <provider
124-->[:react-native-webview] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-16:20
125            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
125-->[:react-native-webview] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-83
126            android:authorities="com.anonymous.QMNotiAugment.fileprovider"
126-->[:react-native-webview] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-64
127            android:exported="false"
127-->[:react-native-webview] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-37
128            android:grantUriPermissions="true" >
128-->[:react-native-webview] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-47
129            <meta-data
129-->[:react-native-webview] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-15:63
130                android:name="android.support.FILE_PROVIDER_PATHS"
130-->[:react-native-webview] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:17-67
131                android:resource="@xml/file_provider_paths" />
131-->[:react-native-webview] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-60
132        </provider>
133
134        <activity
134-->[:expo-dev-launcher] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-25:20
135            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
135-->[:expo-dev-launcher] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-81
136            android:exported="true"
136-->[:expo-dev-launcher] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-36
137            android:launchMode="singleTask"
137-->[:expo-dev-launcher] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-44
138            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
138-->[:expo-dev-launcher] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-70
139            <intent-filter>
139-->[:expo-dev-launcher] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-24:29
140                <action android:name="android.intent.action.VIEW" />
140-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:9:7-58
140-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:9:15-56
141
142                <category android:name="android.intent.category.DEFAULT" />
142-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:25:9-67
142-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:25:19-65
143                <category android:name="android.intent.category.BROWSABLE" />
143-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:10:7-67
143-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:10:17-65
144
145                <data android:scheme="expo-dev-launcher" />
145-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:11:7-37
145-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:11:13-35
146            </intent-filter>
147        </activity>
148        <activity
148-->[:expo-dev-launcher] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:9-29:70
149            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
149-->[:expo-dev-launcher] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-93
150            android:screenOrientation="portrait"
150-->[:expo-dev-launcher] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-49
151            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
151-->[:expo-dev-launcher] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-67
152        <activity
152-->[:expo-dev-menu] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-21:20
153            android:name="expo.modules.devmenu.DevMenuActivity"
153-->[:expo-dev-menu] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-64
154            android:exported="true"
154-->[:expo-dev-menu] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-36
155            android:launchMode="singleTask"
155-->[:expo-dev-menu] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-44
156            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
156-->[:expo-dev-menu] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-75
157            <intent-filter>
157-->[:expo-dev-menu] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-20:29
158                <action android:name="android.intent.action.VIEW" />
158-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:9:7-58
158-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:9:15-56
159
160                <category android:name="android.intent.category.DEFAULT" />
160-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:25:9-67
160-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:25:19-65
161                <category android:name="android.intent.category.BROWSABLE" />
161-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:10:7-67
161-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:10:17-65
162
163                <data android:scheme="expo-dev-menu" />
163-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:11:7-37
163-->/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/AndroidManifest.xml:11:13-35
164            </intent-filter>
165        </activity>
166
167        <meta-data
167-->[:expo-modules-core] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-11:89
168            android:name="org.unimodules.core.AppLoader#react-native-headless"
168-->[:expo-modules-core] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-79
169            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
169-->[:expo-modules-core] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-86
170        <meta-data
170-->[:expo-modules-core] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-15:45
171            android:name="com.facebook.soloader.enabled"
171-->[:expo-modules-core] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-57
172            android:value="true" />
172-->[:expo-modules-core] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-33
173
174        <activity
174-->[com.facebook.react:react-android:0.79.2] /Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/AndroidManifest.xml:19:9-21:40
175            android:name="com.facebook.react.devsupport.DevSettingsActivity"
175-->[com.facebook.react:react-android:0.79.2] /Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/AndroidManifest.xml:20:13-77
176            android:exported="false" />
176-->[com.facebook.react:react-android:0.79.2] /Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/AndroidManifest.xml:21:13-37
177
178        <provider
178-->[:expo-file-system] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:9-30:20
179            android:name="expo.modules.filesystem.FileSystemFileProvider"
179-->[:expo-file-system] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-74
180            android:authorities="com.anonymous.QMNotiAugment.FileSystemFileProvider"
180-->[:expo-file-system] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:13-74
181            android:exported="false"
181-->[:expo-file-system] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:13-37
182            android:grantUriPermissions="true" >
182-->[:expo-file-system] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:13-47
183            <meta-data
183-->[:react-native-webview] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-15:63
184                android:name="android.support.FILE_PROVIDER_PATHS"
184-->[:react-native-webview] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:17-67
185                android:resource="@xml/file_system_provider_paths" />
185-->[:react-native-webview] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-60
186        </provider>
187
188        <meta-data
188-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/fbc6c77c646cf846ed804201d2591aa6/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:11:9-13:43
189            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
189-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/fbc6c77c646cf846ed804201d2591aa6/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:12:13-84
190            android:value="GlideModule" />
190-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/fbc6c77c646cf846ed804201d2591aa6/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:13:13-40
191
192        <service
192-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:9-17:19
193            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
193-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-91
194            android:exported="false" >
194-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-37
195            <intent-filter android:priority="-1" >
195-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-16:29
195-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:28-49
196                <action android:name="com.google.firebase.MESSAGING_EVENT" />
196-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-78
196-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:25-75
197            </intent-filter>
198        </service>
199
200        <receiver
200-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:9-31:20
201            android:name="expo.modules.notifications.service.NotificationsService"
201-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-83
202            android:enabled="true"
202-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-35
203            android:exported="false" >
203-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-37
204            <intent-filter android:priority="-1" >
204-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:13-30:29
204-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:28-49
205                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
205-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:17-88
205-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:25-85
206                <action android:name="android.intent.action.BOOT_COMPLETED" />
206-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:17-79
206-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:25-76
207                <action android:name="android.intent.action.REBOOT" />
207-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:17-71
207-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:25-68
208                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
208-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:17-82
208-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:25-79
209                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
209-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:17-82
209-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:25-79
210                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
210-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:17-84
210-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:25-81
211            </intent-filter>
212        </receiver>
213
214        <activity
214-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:33:9-40:75
215            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
215-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:34:13-92
216            android:excludeFromRecents="true"
216-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:35:13-46
217            android:exported="false"
217-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:36:13-37
218            android:launchMode="standard"
218-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:37:13-42
219            android:noHistory="true"
219-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:38:13-37
220            android:taskAffinity=""
220-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:39:13-36
221            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
221-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:40:13-72
222
223        <receiver
223-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:29:9-40:20
224            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
224-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:30:13-78
225            android:exported="true"
225-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:31:13-36
226            android:permission="com.google.android.c2dm.permission.SEND" >
226-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:32:13-73
227            <intent-filter>
227-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:33:13-35:29
228                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
228-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:34:17-81
228-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:34:25-78
229            </intent-filter>
230
231            <meta-data
231-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:37:13-39:40
232                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
232-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:38:17-92
233                android:value="true" />
233-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:39:17-37
234        </receiver>
235        <!--
236             FirebaseMessagingService performs security checks at runtime,
237             but set to not exported to explicitly avoid allowing another app to call it.
238        -->
239        <service
239-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:46:9-53:19
240            android:name="com.google.firebase.messaging.FirebaseMessagingService"
240-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:47:13-82
241            android:directBootAware="true"
241-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:48:13-43
242            android:exported="false" >
242-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:49:13-37
243            <intent-filter android:priority="-500" >
243-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-16:29
243-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:28-49
244                <action android:name="com.google.firebase.MESSAGING_EVENT" />
244-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-78
244-->[:expo-notifications] /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:25-75
245            </intent-filter>
246        </service>
247        <service
247-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:54:9-63:19
248            android:name="com.google.firebase.components.ComponentDiscoveryService"
248-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:55:13-84
249            android:directBootAware="true"
249-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/4c55f14ee6bfb7ccabedf541c19c7bc6/transformed/firebase-common-21.0.0/AndroidManifest.xml:32:13-43
250            android:exported="false" >
250-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:56:13-37
251            <meta-data
251-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:57:13-59:85
252                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
252-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:58:17-122
253                android:value="com.google.firebase.components.ComponentRegistrar" />
253-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:59:17-82
254            <meta-data
254-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:60:13-62:85
255                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
255-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:61:17-119
256                android:value="com.google.firebase.components.ComponentRegistrar" />
256-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:62:17-82
257            <meta-data
257-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d76105b7a647cc2edc18f5c8ecdb1e99/transformed/firebase-installations-17.2.0/AndroidManifest.xml:15:13-17:85
258                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
258-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d76105b7a647cc2edc18f5c8ecdb1e99/transformed/firebase-installations-17.2.0/AndroidManifest.xml:16:17-130
259                android:value="com.google.firebase.components.ComponentRegistrar" />
259-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d76105b7a647cc2edc18f5c8ecdb1e99/transformed/firebase-installations-17.2.0/AndroidManifest.xml:17:17-82
260            <meta-data
260-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d76105b7a647cc2edc18f5c8ecdb1e99/transformed/firebase-installations-17.2.0/AndroidManifest.xml:18:13-20:85
261                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
261-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d76105b7a647cc2edc18f5c8ecdb1e99/transformed/firebase-installations-17.2.0/AndroidManifest.xml:19:17-127
262                android:value="com.google.firebase.components.ComponentRegistrar" />
262-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d76105b7a647cc2edc18f5c8ecdb1e99/transformed/firebase-installations-17.2.0/AndroidManifest.xml:20:17-82
263            <meta-data
263-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/098b84f38fbd7fb7a201fd8477460ff6/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
264                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
264-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/098b84f38fbd7fb7a201fd8477460ff6/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
265                android:value="com.google.firebase.components.ComponentRegistrar" />
265-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/098b84f38fbd7fb7a201fd8477460ff6/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
266            <meta-data
266-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/4c55f14ee6bfb7ccabedf541c19c7bc6/transformed/firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
267                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
267-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/4c55f14ee6bfb7ccabedf541c19c7bc6/transformed/firebase-common-21.0.0/AndroidManifest.xml:36:17-109
268                android:value="com.google.firebase.components.ComponentRegistrar" />
268-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/4c55f14ee6bfb7ccabedf541c19c7bc6/transformed/firebase-common-21.0.0/AndroidManifest.xml:37:17-82
269            <meta-data
269-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/0297f1c848243ef50e46bd3151056554/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
270                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
270-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/0297f1c848243ef50e46bd3151056554/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
271                android:value="com.google.firebase.components.ComponentRegistrar" />
271-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/0297f1c848243ef50e46bd3151056554/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
272        </service>
273
274        <activity
274-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/80d1f99e1fb93cc95e955d0534b2be1e/transformed/play-services-base-18.0.1/AndroidManifest.xml:20:9-22:45
275            android:name="com.google.android.gms.common.api.GoogleApiActivity"
275-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/80d1f99e1fb93cc95e955d0534b2be1e/transformed/play-services-base-18.0.1/AndroidManifest.xml:20:19-85
276            android:exported="false"
276-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/80d1f99e1fb93cc95e955d0534b2be1e/transformed/play-services-base-18.0.1/AndroidManifest.xml:22:19-43
277            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
277-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/80d1f99e1fb93cc95e955d0534b2be1e/transformed/play-services-base-18.0.1/AndroidManifest.xml:21:19-78
278
279        <provider
279-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/4c55f14ee6bfb7ccabedf541c19c7bc6/transformed/firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
280            android:name="com.google.firebase.provider.FirebaseInitProvider"
280-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/4c55f14ee6bfb7ccabedf541c19c7bc6/transformed/firebase-common-21.0.0/AndroidManifest.xml:24:13-77
281            android:authorities="com.anonymous.QMNotiAugment.firebaseinitprovider"
281-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/4c55f14ee6bfb7ccabedf541c19c7bc6/transformed/firebase-common-21.0.0/AndroidManifest.xml:25:13-72
282            android:directBootAware="true"
282-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/4c55f14ee6bfb7ccabedf541c19c7bc6/transformed/firebase-common-21.0.0/AndroidManifest.xml:26:13-43
283            android:exported="false"
283-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/4c55f14ee6bfb7ccabedf541c19c7bc6/transformed/firebase-common-21.0.0/AndroidManifest.xml:27:13-37
284            android:initOrder="100" />
284-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/4c55f14ee6bfb7ccabedf541c19c7bc6/transformed/firebase-common-21.0.0/AndroidManifest.xml:28:13-36
285        <provider
285-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/42554d3c9533484b9bc0eb513e444050/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
286            android:name="androidx.startup.InitializationProvider"
286-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/42554d3c9533484b9bc0eb513e444050/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
287            android:authorities="com.anonymous.QMNotiAugment.androidx-startup"
287-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/42554d3c9533484b9bc0eb513e444050/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
288            android:exported="false" >
288-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/42554d3c9533484b9bc0eb513e444050/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
289            <meta-data
289-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/42554d3c9533484b9bc0eb513e444050/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
290                android:name="androidx.emoji2.text.EmojiCompatInitializer"
290-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/42554d3c9533484b9bc0eb513e444050/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
291                android:value="androidx.startup" />
291-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/42554d3c9533484b9bc0eb513e444050/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
292            <meta-data
292-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/3ac5a04d60853b9fc4bb91d52842e99f/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
293                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
293-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/3ac5a04d60853b9fc4bb91d52842e99f/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
294                android:value="androidx.startup" />
294-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/3ac5a04d60853b9fc4bb91d52842e99f/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
295            <meta-data
295-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
296                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
296-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
297                android:value="androidx.startup" />
297-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
298        </provider>
299
300        <meta-data
300-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/d6cc7e96abcd1f2665995aea30d859a4/transformed/play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
301            android:name="com.google.android.gms.version"
301-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/d6cc7e96abcd1f2665995aea30d859a4/transformed/play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
302            android:value="@integer/google_play_services_version" />
302-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/d6cc7e96abcd1f2665995aea30d859a4/transformed/play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
303
304        <receiver
304-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
305            android:name="androidx.profileinstaller.ProfileInstallReceiver"
305-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
306            android:directBootAware="false"
306-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
307            android:enabled="true"
307-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
308            android:exported="true"
308-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
309            android:permission="android.permission.DUMP" >
309-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
310            <intent-filter>
310-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
311                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
311-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
311-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
312            </intent-filter>
313            <intent-filter>
313-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
314                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
314-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
314-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
315            </intent-filter>
316            <intent-filter>
316-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
317                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
317-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
317-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
318            </intent-filter>
319            <intent-filter>
319-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
320                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
320-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
320-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
321            </intent-filter>
322        </receiver>
323
324        <service
324-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/490fb3dcdd4e0105f0d54840f1711ab0/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
325            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
325-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/490fb3dcdd4e0105f0d54840f1711ab0/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
326            android:exported="false" >
326-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/490fb3dcdd4e0105f0d54840f1711ab0/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
327            <meta-data
327-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/490fb3dcdd4e0105f0d54840f1711ab0/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
328                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
328-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/490fb3dcdd4e0105f0d54840f1711ab0/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
329                android:value="cct" />
329-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/490fb3dcdd4e0105f0d54840f1711ab0/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
330        </service>
331        <service
331-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/95d1294fc77e9bad826f6494d3de3606/transformed/transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
332            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
332-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/95d1294fc77e9bad826f6494d3de3606/transformed/transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
333            android:exported="false"
333-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/95d1294fc77e9bad826f6494d3de3606/transformed/transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
334            android:permission="android.permission.BIND_JOB_SERVICE" >
334-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/95d1294fc77e9bad826f6494d3de3606/transformed/transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
335        </service>
336
337        <receiver
337-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/95d1294fc77e9bad826f6494d3de3606/transformed/transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
338            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
338-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/95d1294fc77e9bad826f6494d3de3606/transformed/transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
339            android:exported="false" />
339-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/95d1294fc77e9bad826f6494d3de3606/transformed/transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
340    </application>
341
342</manifest>
