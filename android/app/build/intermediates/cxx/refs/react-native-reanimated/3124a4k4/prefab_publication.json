{"installationFolder": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-reanimated/android/build/intermediates/prefab_package/debug/prefab", "gradlePath": ":react-native-reanimated", "packageInfo": {"packageName": "react-native-reanimated", "packageVersion": "3.17.5", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "reanimated", "moduleHeaders": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-reanimated/android/build/prefab-headers/reanimated", "moduleExportLibraries": [], "abis": [{"abiName": "armeabi-v7a", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/z1o292k2/obj/armeabi-v7a/libreanimated.so", "abiAndroidGradleBuildJsonFile": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-reanimated/android/.cxx/Debug/z1o292k2/armeabi-v7a/android_gradle_build.json"}]}, {"moduleName": "worklets", "moduleHeaders": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-reanimated/android/build/prefab-headers/worklets", "moduleExportLibraries": [], "abis": [{"abiName": "armeabi-v7a", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/z1o292k2/obj/armeabi-v7a/libworklets.so", "abiAndroidGradleBuildJsonFile": "/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-reanimated/android/.cxx/Debug/z1o292k2/armeabi-v7a/android_gradle_build.json"}]}]}}