/opt/homebrew/Cellar/openjdk@17/17.0.15/libexec/openjdk.jdk/Contents/Home/bin/java \
  --class-path \
  /Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.prefab/cli/2.1.0/aa32fec809c44fa531f01dcfb739b5b3304d3050/cli-2.1.0-all.jar \
  com.google.prefab.cli.AppKt \
  --build-system \
  cmake \
  --platform \
  android \
  --abi \
  arm64-v8a \
  --os-version \
  24 \
  --stl \
  c++_shared \
  --ndk-version \
  27 \
  --output \
  /var/folders/zn/v_q4xkm97ps6lqsrzbc2tk7w0000gp/T/agp-prefab-staging11070344541179032685/staged-cli-output \
  /Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab \
  /Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/intermediates/cxx/refs/react-native-reanimated/251b552x \
  /Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab \
  /Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab
