com.anonymous.QMNotiAugment.app-startup-runtime-1.1.1-0 /Users/<USER>/.gradle/caches/8.13/transforms/00774c690ca55720da1859b878e48512/transformed/startup-runtime-1.1.1/res
com.anonymous.QMNotiAugment.app-frameanimation-3.0.3-1 /Users/<USER>/.gradle/caches/8.13/transforms/05a7daa60ae8e61fb95383ac8acc272d/transformed/frameanimation-3.0.3/res
com.anonymous.QMNotiAugment.app-appcompat-resources-1.7.0-2 /Users/<USER>/.gradle/caches/8.13/transforms/09494fb4bc8f220614094cdf67a9b197/transformed/appcompat-resources-1.7.0/res
com.anonymous.QMNotiAugment.app-gif-3.0.3-3 /Users/<USER>/.gradle/caches/8.13/transforms/0c193342b800a8ea97e3bdf5eaf38463/transformed/gif-3.0.3/res
com.anonymous.QMNotiAugment.app-activity-ktx-1.8.0-4 /Users/<USER>/.gradle/caches/8.13/transforms/116c308dcd0af56677669e38e3b2a9e4/transformed/activity-ktx-1.8.0/res
com.anonymous.QMNotiAugment.app-lifecycle-livedata-core-2.6.2-5 /Users/<USER>/.gradle/caches/8.13/transforms/141de2705609c2aefea70a7a6ec96916/transformed/lifecycle-livedata-core-2.6.2/res
com.anonymous.QMNotiAugment.app-drawerlayout-1.1.1-6 /Users/<USER>/.gradle/caches/8.13/transforms/1727a3cb27ebdef3154cd7c2211b8b98/transformed/drawerlayout-1.1.1/res
com.anonymous.QMNotiAugment.app-lifecycle-viewmodel-2.6.2-7 /Users/<USER>/.gradle/caches/8.13/transforms/1faab334db77e08cdd8e99a4aef0ffa3/transformed/lifecycle-viewmodel-2.6.2/res
com.anonymous.QMNotiAugment.app-appcompat-1.7.0-8 /Users/<USER>/.gradle/caches/8.13/transforms/25dd2e9a2ec645a6ceb46052888a9d56/transformed/appcompat-1.7.0/res
com.anonymous.QMNotiAugment.app-awebp-3.0.3-9 /Users/<USER>/.gradle/caches/8.13/transforms/2c8964cc86241d9329e4cfc32db0733c/transformed/awebp-3.0.3/res
com.anonymous.QMNotiAugment.app-fragment-1.6.1-10 /Users/<USER>/.gradle/caches/8.13/transforms/2d501250e312faf23654afeb26f10903/transformed/fragment-1.6.1/res
com.anonymous.QMNotiAugment.app-profileinstaller-1.3.1-11 /Users/<USER>/.gradle/caches/8.13/transforms/3273770dd9b216f26223a6f98644967e/transformed/profileinstaller-1.3.1/res
com.anonymous.QMNotiAugment.app-transition-1.5.0-12 /Users/<USER>/.gradle/caches/8.13/transforms/379aca9cadd8bb41b8a17f7b6c2df346/transformed/transition-1.5.0/res
com.anonymous.QMNotiAugment.app-lifecycle-process-2.6.2-13 /Users/<USER>/.gradle/caches/8.13/transforms/3ac5a04d60853b9fc4bb91d52842e99f/transformed/lifecycle-process-2.6.2/res
com.anonymous.QMNotiAugment.app-lifecycle-livedata-core-ktx-2.6.2-14 /Users/<USER>/.gradle/caches/8.13/transforms/3e295a72abacc6ce20dc0ca41a996c76/transformed/lifecycle-livedata-core-ktx-2.6.2/res
com.anonymous.QMNotiAugment.app-cardview-1.0.0-15 /Users/<USER>/.gradle/caches/8.13/transforms/3ffb45bef6d4684be778fb0b427ccf0e/transformed/cardview-1.0.0/res
com.anonymous.QMNotiAugment.app-emoji2-1.3.0-16 /Users/<USER>/.gradle/caches/8.13/transforms/42554d3c9533484b9bc0eb513e444050/transformed/emoji2-1.3.0/res
com.anonymous.QMNotiAugment.app-material-1.12.0-17 /Users/<USER>/.gradle/caches/8.13/transforms/429888db8e5916503c7ec8b30991ae80/transformed/material-1.12.0/res
com.anonymous.QMNotiAugment.app-androidsvg-aar-1.4-18 /Users/<USER>/.gradle/caches/8.13/transforms/4337afb52a3270499dcf9191ac8fe7ea/transformed/androidsvg-aar-1.4/res
com.anonymous.QMNotiAugment.app-avif-3.0.3-19 /Users/<USER>/.gradle/caches/8.13/transforms/44ab755464896b6133708d415e71f91a/transformed/avif-3.0.3/res
com.anonymous.QMNotiAugment.app-core-1.13.1-20 /Users/<USER>/.gradle/caches/8.13/transforms/480ce62020c830cf93653ad6a17bbf11/transformed/core-1.13.1/res
com.anonymous.QMNotiAugment.app-react-android-0.79.2-debug-21 /Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/res
com.anonymous.QMNotiAugment.app-recyclerview-1.1.0-22 /Users/<USER>/.gradle/caches/8.13/transforms/493de7f104d0479b26aa33c67c4bc71a/transformed/recyclerview-1.1.0/res
com.anonymous.QMNotiAugment.app-firebase-common-21.0.0-23 /Users/<USER>/.gradle/caches/8.13/transforms/4c55f14ee6bfb7ccabedf541c19c7bc6/transformed/firebase-common-21.0.0/res
com.anonymous.QMNotiAugment.app-swiperefreshlayout-1.1.0-24 /Users/<USER>/.gradle/caches/8.13/transforms/5207d0367f06c1f6e84cbefae3e2b587/transformed/swiperefreshlayout-1.1.0/res
com.anonymous.QMNotiAugment.app-lifecycle-runtime-2.6.2-25 /Users/<USER>/.gradle/caches/8.13/transforms/5567384c0811be2fdff4aa86a52ddb5b/transformed/lifecycle-runtime-2.6.2/res
com.anonymous.QMNotiAugment.app-core-runtime-2.2.0-26 /Users/<USER>/.gradle/caches/8.13/transforms/55a5428e2997af18cb564ece6f864d2d/transformed/core-runtime-2.2.0/res
com.anonymous.QMNotiAugment.app-browser-1.6.0-27 /Users/<USER>/.gradle/caches/8.13/transforms/582d0c2f5e6292ce32dc808d4c3bbb90/transformed/browser-1.6.0/res
com.anonymous.QMNotiAugment.app-lifecycle-viewmodel-savedstate-2.6.2-28 /Users/<USER>/.gradle/caches/8.13/transforms/645235fd5f58e6da3a574b83261b74f1/transformed/lifecycle-viewmodel-savedstate-2.6.2/res
com.anonymous.QMNotiAugment.app-emoji2-views-helper-1.3.0-29 /Users/<USER>/.gradle/caches/8.13/transforms/6453ecd10fa736e3e1a54637aa44062a/transformed/emoji2-views-helper-1.3.0/res
com.anonymous.QMNotiAugment.app-core-ktx-1.13.1-30 /Users/<USER>/.gradle/caches/8.13/transforms/67edbce12d482f99ad850ac6e27ba795/transformed/core-ktx-1.13.1/res
com.anonymous.QMNotiAugment.app-annotation-experimental-1.4.0-31 /Users/<USER>/.gradle/caches/8.13/transforms/6db73666d44fc921249f2da839274405/transformed/annotation-experimental-1.4.0/res
com.anonymous.QMNotiAugment.app-lifecycle-viewmodel-ktx-2.6.2-32 /Users/<USER>/.gradle/caches/8.13/transforms/7a95eb76eac15d8949b90e2b41aa5f8b/transformed/lifecycle-viewmodel-ktx-2.6.2/res
com.anonymous.QMNotiAugment.app-media-1.0.0-33 /Users/<USER>/.gradle/caches/8.13/transforms/7df43354cc9c88f43706e2de371bba77/transformed/media-1.0.0/res
com.anonymous.QMNotiAugment.app-play-services-base-18.0.1-34 /Users/<USER>/.gradle/caches/8.13/transforms/80d1f99e1fb93cc95e955d0534b2be1e/transformed/play-services-base-18.0.1/res
com.anonymous.QMNotiAugment.app-viewpager2-1.0.0-35 /Users/<USER>/.gradle/caches/8.13/transforms/8315ec2b76c6dfb54a9129d88f0598c1/transformed/viewpager2-1.0.0/res
com.anonymous.QMNotiAugment.app-lifecycle-service-2.6.2-36 /Users/<USER>/.gradle/caches/8.13/transforms/852be9e6679aa434c2578b30b83f2ed6/transformed/lifecycle-service-2.6.2/res
com.anonymous.QMNotiAugment.app-savedstate-ktx-1.2.1-37 /Users/<USER>/.gradle/caches/8.13/transforms/89a14f92f7d6a0d0a0ff24a0417f205f/transformed/savedstate-ktx-1.2.1/res
com.anonymous.QMNotiAugment.app-activity-1.8.0-38 /Users/<USER>/.gradle/caches/8.13/transforms/8a69babe9d9651d03b9a971a79e86412/transformed/activity-1.8.0/res
com.anonymous.QMNotiAugment.app-tracing-1.2.0-39 /Users/<USER>/.gradle/caches/8.13/transforms/8cd6302452352550fbfa35c1ffc800a8/transformed/tracing-1.2.0/res
com.anonymous.QMNotiAugment.app-autofill-1.1.0-40 /Users/<USER>/.gradle/caches/8.13/transforms/93122185410ef064caadb5d5229c2b16/transformed/autofill-1.1.0/res
com.anonymous.QMNotiAugment.app-expo.modules.systemui-5.0.7-41 /Users/<USER>/.gradle/caches/8.13/transforms/9915be2300aeb0d47bd05a4e69d44d77/transformed/expo.modules.systemui-5.0.7/res
com.anonymous.QMNotiAugment.app-constraintlayout-2.0.1-42 /Users/<USER>/.gradle/caches/8.13/transforms/992ae729079d0d96d05b1ce2a0b0c65e/transformed/constraintlayout-2.0.1/res
com.anonymous.QMNotiAugment.app-glide-4.16.0-43 /Users/<USER>/.gradle/caches/8.13/transforms/acd1680e29a2bb5febce35f72e18cd41/transformed/glide-4.16.0/res
com.anonymous.QMNotiAugment.app-expo.modules.splashscreen-0.30.8-44 /Users/<USER>/.gradle/caches/8.13/transforms/b10e047db977ffb3716f52022c3b241d/transformed/expo.modules.splashscreen-0.30.8/res
com.anonymous.QMNotiAugment.app-glide-plugin-3.0.3-45 /Users/<USER>/.gradle/caches/8.13/transforms/b8f05299ecf36b7f0a10382396160338/transformed/glide-plugin-3.0.3/res
com.anonymous.QMNotiAugment.app-core-splashscreen-1.2.0-alpha02-46 /Users/<USER>/.gradle/caches/8.13/transforms/cb430efb3b96d95de96e855fce06cf16/transformed/core-splashscreen-1.2.0-alpha02/res
com.anonymous.QMNotiAugment.app-fragment-ktx-1.6.1-47 /Users/<USER>/.gradle/caches/8.13/transforms/d22d246360e800147b80b90f315901c5/transformed/fragment-ktx-1.6.1/res
com.anonymous.QMNotiAugment.app-firebase-messaging-24.0.1-48 /Users/<USER>/.gradle/caches/8.13/transforms/d6af5baa27348c57aa82393776411b37/transformed/firebase-messaging-24.0.1/res
com.anonymous.QMNotiAugment.app-play-services-basement-18.3.0-49 /Users/<USER>/.gradle/caches/8.13/transforms/d6cc7e96abcd1f2665995aea30d859a4/transformed/play-services-basement-18.3.0/res
com.anonymous.QMNotiAugment.app-lifecycle-runtime-ktx-2.6.2-50 /Users/<USER>/.gradle/caches/8.13/transforms/da827b85871f4bd0abe0652a3c1ed191/transformed/lifecycle-runtime-ktx-2.6.2/res
com.anonymous.QMNotiAugment.app-savedstate-1.2.1-51 /Users/<USER>/.gradle/caches/8.13/transforms/dbba9395a04335ba141d79ea360d770f/transformed/savedstate-1.2.1/res
com.anonymous.QMNotiAugment.app-apng-3.0.3-52 /Users/<USER>/.gradle/caches/8.13/transforms/e11000938d50f861abe720434978a0e8/transformed/apng-3.0.3/res
com.anonymous.QMNotiAugment.app-lifecycle-livedata-2.6.2-53 /Users/<USER>/.gradle/caches/8.13/transforms/e53974698227bf58e8a34890a5d6dc1b/transformed/lifecycle-livedata-2.6.2/res
com.anonymous.QMNotiAugment.app-coordinatorlayout-1.2.0-54 /Users/<USER>/.gradle/caches/8.13/transforms/e67554b16c0f66ed5dcd94f202e15e5c/transformed/coordinatorlayout-1.2.0/res
com.anonymous.QMNotiAugment.app-BlurView-version-2.0.6-55 /Users/<USER>/.gradle/caches/8.13/transforms/f209de837fc39a1abddedcfd1cc7b64d/transformed/BlurView-version-2.0.6/res
com.anonymous.QMNotiAugment.app-drawee-3.6.0-56 /Users/<USER>/.gradle/caches/8.13/transforms/f279fa17c01dbfb036d3f1125112bf06/transformed/drawee-3.6.0/res
com.anonymous.QMNotiAugment.app-tracing-ktx-1.2.0-57 /Users/<USER>/.gradle/caches/8.13/transforms/f3a748209e608070e671341ac3c4992a/transformed/tracing-ktx-1.2.0/res
com.anonymous.QMNotiAugment.app-pngs-58 /Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/generated/res/pngs/debug
com.anonymous.QMNotiAugment.app-resValues-59 /Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/generated/res/resValues/debug
com.anonymous.QMNotiAugment.app-packageDebugResources-60 /Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/intermediates/incremental/debug/packageDebugResources/merged.dir
com.anonymous.QMNotiAugment.app-packageDebugResources-61 /Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/intermediates/incremental/debug/packageDebugResources/stripped.dir
com.anonymous.QMNotiAugment.app-debug-62 /Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/intermediates/merged_res/debug/mergeDebugResources
com.anonymous.QMNotiAugment.app-debug-63 /Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/debug/res
com.anonymous.QMNotiAugment.app-main-64 /Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/res
com.anonymous.QMNotiAugment.app-debug-65 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-66 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-vector-icons/material-design-icons/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-67 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-constants/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-68 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-client/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-69 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-70 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu-interface/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-71 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-72 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-file-system/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-73 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-json-utils/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-74 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-linking/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-75 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-manifests/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-76 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-modules-core/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-77 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-78 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-updates-interface/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-79 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-80 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-edge-to-edge/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-81 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-gesture-handler/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-82 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-reanimated/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-83 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-safe-area-context/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-84 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-85 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-vector-icons/android/build/intermediates/packaged_res/debug/packageDebugResources
com.anonymous.QMNotiAugment.app-debug-86 /Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-webview/android/build/intermediates/packaged_res/debug/packageDebugResources
