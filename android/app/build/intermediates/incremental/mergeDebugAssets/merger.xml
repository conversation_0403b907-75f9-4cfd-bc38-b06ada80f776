<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":expo-updates-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-updates-interface/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-linking" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-linking/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-json-utils" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-json-utils/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-manifests" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-manifests/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-dev-client" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-client/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-constants/android/build/intermediates/library_assets/debug/packageDebugAssets/out"><file name="app.config" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-constants/android/build/intermediates/library_assets/debug/packageDebugAssets/out/app.config"/></source></dataSet><dataSet config=":expo-notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-notifications/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-file-system" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-file-system/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-modules-core/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-dev-menu-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu-interface/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-dev-menu" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out"><file name="Inter-Bold.otf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Bold.otf"/><file name="Inter-Regular.otf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Regular.otf"/><file name="Inter-Thin.otf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Thin.otf"/><file name="Inter-Light.otf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Light.otf"/><file name="EXDevMenuApp.android.js" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/EXDevMenuApp.android.js"/><file name="Inter-Medium.otf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Medium.otf"/><file name="Inter-SemiBold.otf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-SemiBold.otf"/><file name="dev-menu-packager-host" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/dev-menu-packager-host"/><file name="Inter-Black.otf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Black.otf"/><file name="Inter-ExtraBold.otf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-ExtraBold.otf"/><file name="Inter-ExtraLight.otf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-ExtraLight.otf"/></source></dataSet><dataSet config=":expo-dev-launcher" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out"><file name="Inter-Bold.otf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Bold.otf"/><file name="Inter-Regular.otf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Regular.otf"/><file name="Inter-Thin.otf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Thin.otf"/><file name="Inter-Light.otf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Light.otf"/><file name="expo_dev_launcher_android.bundle" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/expo_dev_launcher_android.bundle"/><file name="Inter-Medium.otf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Medium.otf"/><file name="Inter-SemiBold.otf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-SemiBold.otf"/><file name="Inter-Black.otf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Black.otf"/><file name="Inter-ExtraBold.otf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-ExtraBold.otf"/><file name="Inter-ExtraLight.otf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-ExtraLight.otf"/></source></dataSet><dataSet config=":react-native-vector-icons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-vector-icons/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-reanimated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-reanimated/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/expo/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-edge-to-edge" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-edge-to-edge/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-webview" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-webview/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-screens/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-safe-area-context/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/react-native-gesture-handler/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-vector-icons_material-design-icons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-vector-icons/material-design-icons/android/build/intermediates/library_assets/debug/packageDebugAssets/out"><file name="fonts/MaterialDesignIcons.ttf" path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/node_modules/@react-native-vector-icons/material-design-icons/android/build/intermediates/library_assets/debug/packageDebugAssets/out/fonts/MaterialDesignIcons.ttf"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/main/assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/intermediates/shader_assets/debug/compileDebugShaders/out"/></dataSet></merger>