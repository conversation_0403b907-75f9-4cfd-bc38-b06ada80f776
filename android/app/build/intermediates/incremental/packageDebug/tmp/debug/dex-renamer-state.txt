#Mon May 26 18:22:21 HKT 2025
path.4=14/classes.dex
path.3=11/classes.dex
path.2=0/classes.dex
path.1=0/classes.dex
path.6=classes3.dex
path.5=classes2.dex
path.0=classes.dex
base.4=/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/intermediates/dex/debug/mergeProjectDexDebug/14/classes.dex
base.3=/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/intermediates/dex/debug/mergeProjectDexDebug/11/classes.dex
base.2=/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/intermediates/dex/debug/mergeProjectDexDebug/0/classes.dex
base.1=/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/intermediates/dex/debug/mergeLibDexDebug/0/classes.dex
base.0=/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/intermediates/dex/debug/mergeExtDexDebug/classes.dex
renamed.3=classes4.dex
renamed.2=classes3.dex
renamed.1=classes2.dex
renamed.0=classes.dex
renamed.6=classes7.dex
renamed.5=classes6.dex
base.6=/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/intermediates/dex/debug/mergeExtDexDebug/classes3.dex
renamed.4=classes5.dex
base.5=/Users/<USER>/ReactNativeProjects/QMNotiAugment/android/app/build/intermediates/dex/debug/mergeExtDexDebug/classes2.dex
