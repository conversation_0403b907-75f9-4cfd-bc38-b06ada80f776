{"logs": [{"outputFile": "com.anonymous.QMNotiAugment.app-mergeDebugResources-60:/values-fr/values-fr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/d6cc7e96abcd1f2665995aea30d859a4/transformed/play-services-basement-18.3.0/res/values-fr/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "160", "endOffsets": "355"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5779", "endColumns": "164", "endOffsets": "5939"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/480ce62020c830cf93653ad6a17bbf11/transformed/core-1.13.1/res/values-fr/values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "39,40,41,42,43,44,45,157", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3593,3691,3793,3892,3994,4098,4202,14413", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "3686,3788,3887,3989,4093,4197,4315,14509"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/582d0c2f5e6292ce32dc808d4c3bbb90/transformed/browser-1.6.0/res/values-fr/values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "68,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "7118,7453,7555,7674", "endColumns": "106,101,118,104", "endOffsets": "7220,7550,7669,7774"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/25dd2e9a2ec645a6ceb46052888a9d56/transformed/appcompat-1.7.0/res/values-fr/values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,559,669,751,857,987,1065,1141,1232,1325,1423,1518,1618,1711,1804,1899,1990,2081,2167,2277,2388,2491,2602,2710,2817,2976,13443", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,554,664,746,852,982,1060,1136,1227,1320,1418,1513,1613,1706,1799,1894,1985,2076,2162,2272,2383,2486,2597,2705,2812,2971,3070,13525"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/res/values-fr/values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,208,278,361,428,507,589,679,771,842,929,1004,1091,1171,1251,1326,1403,1476,1567,1646,1727,1799", "endColumns": "69,82,69,82,66,78,81,89,91,70,86,74,86,79,79,74,76,72,90,78,80,71,79", "endOffsets": "120,203,273,356,423,502,584,674,766,837,924,999,1086,1166,1246,1321,1398,1471,1562,1641,1722,1794,1874"}, "to": {"startLines": "33,49,75,77,79,93,94,141,142,143,144,149,150,151,152,153,154,155,156,158,159,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3075,4633,7779,7932,8081,9153,9232,13103,13193,13285,13356,13775,13850,13937,14017,14097,14172,14249,14322,14514,14593,14674,14746", "endColumns": "69,82,69,82,66,78,81,89,91,70,86,74,86,79,79,74,76,72,90,78,80,71,79", "endOffsets": "3140,4711,7844,8010,8143,9227,9309,13188,13280,13351,13438,13845,13932,14012,14092,14167,14244,14317,14408,14588,14669,14741,14821"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/80d1f99e1fb93cc95e955d0534b2be1e/transformed/play-services-base-18.0.1/res/values-fr/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,471,597,702,869,998,1115,1224,1398,1506,1687,1819,1975,2150,2219,2282", "endColumns": "101,175,125,104,166,128,116,108,173,107,180,131,155,174,68,62,79", "endOffsets": "294,470,596,701,868,997,1114,1223,1397,1505,1686,1818,1974,2149,2218,2281,2361"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4716,4822,5002,5132,5241,5412,5545,5666,5944,6122,6234,6419,6555,6715,6894,6967,7034", "endColumns": "105,179,129,108,170,132,120,112,177,111,184,135,159,178,72,66,83", "endOffsets": "4817,4997,5127,5236,5407,5540,5661,5774,6117,6229,6414,6550,6710,6889,6962,7029,7113"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/429888db8e5916503c7ec8b30991ae80/transformed/material-1.12.0/res/values-fr/values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1109,1175,1272,1355,1421,1523,1588,1663,1719,1798,1858,1912,2034,2093,2155,2209,2291,2426,2518,2593,2688,2769,2853,2997,3076,3157,3298,3391,3470,3525,3576,3642,3722,3803,3874,3954,4027,4105,4178,4250,4362,4455,4527,4619,4711,4785,4869,4961,5018,5102,5168,5251,5338,5400,5464,5527,5605,5707,5811,5908,6012,6071,6126,6215,6302,6379", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "278,358,439,522,631,726,824,954,1039,1104,1170,1267,1350,1416,1518,1583,1658,1714,1793,1853,1907,2029,2088,2150,2204,2286,2421,2513,2588,2683,2764,2848,2992,3071,3152,3293,3386,3465,3520,3571,3637,3717,3798,3869,3949,4022,4100,4173,4245,4357,4450,4522,4614,4706,4780,4864,4956,5013,5097,5163,5246,5333,5395,5459,5522,5600,5702,5806,5903,6007,6066,6121,6210,6297,6374,6455"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,69,70,71,76,78,80,81,82,83,84,85,86,87,88,89,90,91,92,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,146,147,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3145,3225,3306,3389,3498,4320,4418,4548,7225,7290,7356,7849,8015,8148,8250,8315,8390,8446,8525,8585,8639,8761,8820,8882,8936,9018,9314,9406,9481,9576,9657,9741,9885,9964,10045,10186,10279,10358,10413,10464,10530,10610,10691,10762,10842,10915,10993,11066,11138,11250,11343,11415,11507,11599,11673,11757,11849,11906,11990,12056,12139,12226,12288,12352,12415,12493,12595,12699,12796,12900,12959,13014,13530,13617,13694", "endLines": "5,34,35,36,37,38,46,47,48,69,70,71,76,78,80,81,82,83,84,85,86,87,88,89,90,91,92,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,146,147,148", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "328,3220,3301,3384,3493,3588,4413,4543,4628,7285,7351,7448,7927,8076,8245,8310,8385,8441,8520,8580,8634,8756,8815,8877,8931,9013,9148,9401,9476,9571,9652,9736,9880,9959,10040,10181,10274,10353,10408,10459,10525,10605,10686,10757,10837,10910,10988,11061,11133,11245,11338,11410,11502,11594,11668,11752,11844,11901,11985,12051,12134,12221,12283,12347,12410,12488,12590,12694,12791,12895,12954,13009,13098,13612,13689,13770"}}]}]}